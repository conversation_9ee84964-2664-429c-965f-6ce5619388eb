"""
Main trading bot application
"""

import logging
import time
import signal
import sys
from datetime import datetime
from typing import Dict

from .config import *
from .data_manager import DataManager
from .strategy_engine import StrategyEngine
from .chart_generator import ChartGenerator
from .binance_client import BinanceWebSocketClient, BinanceDataFetcher, create_binance_hook
from .trade_engine import TradeEngine

# Create logs directory if it doesn't exist
import os
os.makedirs(LOGS_DIR, exist_ok=True)

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'{LOGS_DIR}/trading_bot.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class TradingBot:
    """Main trading bot class"""

    def __init__(self):
        self.data_manager = DataManager()
        self.strategy_engine = StrategyEngine(self.data_manager)
        self.chart_generator = ChartGenerator(self.data_manager)
        self.backtest_engine = TradeEngine(self.data_manager, self.strategy_engine, trade_mode="test")
        self.live_trade_engine = TradeEngine(self.data_manager, self.strategy_engine, trade_mode="live")

        self.ws_client = None
        self.is_running = False

        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def initialize_data(self, symbol: str = "BTCUSD"):
        """Initialize historical data"""
        try:
            logger.info(f"Initializing data for {symbol}")

            # Create directories
            import os
            os.makedirs(DATA_DIR, exist_ok=True)
            os.makedirs(CHARTS_DIR, exist_ok=True)
            os.makedirs(LOGS_DIR, exist_ok=True)

            # Load existing data or fetch from Binance
            for timeframe in TIMEFRAMES:
                success = self.data_manager.load_data(symbol, timeframe)
                if not success:
                    logger.warning(f"Failed to load data for {symbol} {timeframe}")

                    # Try to fetch from Binance if 1-minute data
                    if timeframe == 1:
                        self._fetch_historical_data(symbol)
                        # Retry loading
                        self.data_manager.load_data(symbol, timeframe)

            logger.info("Data initialization completed")
            return True

        except Exception as e:
            logger.error(f"Error initializing data: {e}")
            return False

    def _fetch_historical_data(self, symbol: str):
        """Fetch historical data from Binance"""
        try:
            logger.info(f"Fetching historical data for {symbol}")

            fetcher = BinanceDataFetcher()

            # Convert symbol format (BTCUSD -> BTCUSDT)
            binance_symbol = symbol + "T" if symbol.endswith("USD") else symbol

            # Fetch 1-minute data
            klines = fetcher.fetch_historical_klines(binance_symbol, "1m", 1000)

            if klines:
                filename = f"{symbol}_1.csv"
                fetcher.save_to_csv(klines, filename)
                logger.info(f"Historical data saved for {symbol}")
            else:
                logger.error(f"Failed to fetch historical data for {symbol}")

        except Exception as e:
            logger.error(f"Error fetching historical data: {e}")



    def start_live_trading(self, symbol: str = "BTCUSD"):
        """Start live trading with WebSocket connection"""
        try:
            logger.info(f"Starting live trading for {symbol}")

            # Initialize data first
            if not self.initialize_data(symbol):
                logger.error("Failed to initialize data")
                return False

            # Create Binance hook with live trade engine integration
            binance_hook = create_binance_hook(
                self.data_manager,
                self.strategy_engine,
                self.chart_generator,
                self.live_trade_engine
            )

            # Convert symbol format for Binance
            binance_symbol = symbol + "T" if symbol.endswith("USD") else symbol

            # Create WebSocket client
            self.ws_client = BinanceWebSocketClient(binance_hook)

            # Connect to WebSocket
            if not self.ws_client.connect(binance_symbol):
                logger.error("Failed to connect to Binance WebSocket")
                return False

            self.is_running = True
            logger.info("Live trading started successfully")

            # Keep the bot running
            self._run_main_loop()

            return True

        except Exception as e:
            logger.error(f"Error starting live trading: {e}")
            return False

    def _run_main_loop(self):
        """Main loop for the trading bot"""
        try:
            logger.info("Entering main trading loop")

            while self.is_running:
                try:
                    # Check WebSocket connection
                    if self.ws_client and not self.ws_client.is_alive():
                        logger.warning("WebSocket connection lost, attempting to reconnect...")
                        # Reconnection is handled automatically by the WebSocket client

                    # Log memory usage periodically
                    if hasattr(self, '_last_memory_log'):
                        if time.time() - self._last_memory_log > 300:  # Every 5 minutes
                            self._log_memory_usage()
                            self._last_memory_log = time.time()
                    else:
                        self._last_memory_log = time.time()

                    # Sleep to prevent high CPU usage
                    time.sleep(1)

                except KeyboardInterrupt:
                    logger.info("Received keyboard interrupt")
                    break
                except Exception as e:
                    logger.error(f"Error in main loop: {e}")
                    time.sleep(5)  # Wait before continuing

            logger.info("Main trading loop ended")

        except Exception as e:
            logger.error(f"Error in main loop: {e}")

    def _log_memory_usage(self):
        """Log current memory usage"""
        try:
            memory_info = self.data_manager.get_memory_usage()
            total_memory = 0

            for symbol, timeframes in memory_info.items():
                for tf, info in timeframes.items():
                    total_memory += info['memory_mb']

            logger.info(f"Total memory usage: {total_memory:.2f} MB")

        except Exception as e:
            logger.error(f"Error logging memory usage: {e}")

    def run_backtest(self, strategy_filter: str, symbol: str = "BTCUSD", rows_back: int = 44000,
                    initial_balance: float = 10000.0, risk_per_trade: float = 0.02) -> bool:
        """
        Run backtest

        Args:
            symbol: Trading symbol
            rows_back: Number of rows (1-minute candles) to backtest from the tail
            initial_balance: Starting balance
            risk_per_trade: Risk percentage per trade
            strategy_filter: Strategy name to test (required)
        """
        try:
            logger.info(f"Starting backtest for {symbol} with strategy: {strategy_filter}")

            # Validate strategy_filter
            if not strategy_filter:
                logger.error("strategy_filter is required for backtest")
                return False

            # Initialize data
            if not self.initialize_data(symbol):
                logger.error("Failed to initialize data")
                return False

            # Get the full 1-minute dataframe
            df_1m = self.data_manager.get_dataframe(symbol, 1)
            if df_1m is None or len(df_1m) == 0:
                logger.error(f"No 1-minute data available for {symbol}")
                return False

            # Get the last N rows (tail)
            df_tail = df_1m.tail(rows_back)
            if len(df_tail) == 0:
                logger.error(f"No data available for the last {rows_back} rows")
                return False

            logger.info(f"Using last {len(df_tail)} rows of 1-minute data for backtest")

            # Get start and end dates from the tail data
            start_date = df_tail.index[0]
            end_date = df_tail.index[-1]

            # Run backtest
            result = self.backtest_engine.run_backtest(
                symbol=symbol,
                start_date=start_date,
                end_date=end_date,
                strategy_filter=strategy_filter,
                initial_balance=initial_balance,
                risk_per_trade=risk_per_trade
            )

            # Print results
            self._print_backtest_results(result)

            # Save results
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"backtest_{symbol}_{timestamp}.pkl"
            self.backtest_engine.save_results(result, filename)

            # Generate summary chart
            chart_path = self.chart_generator.create_backtest_summary_chart(
                [result.to_dict()]
            )
            if chart_path:
                logger.info(f"Backtest summary chart saved: {chart_path}")

            return True

        except Exception as e:
            logger.error(f"Error running backtest: {e}")
            return False

    def get_live_trading_status(self) -> Dict:
        """Get current live trading status"""
        try:
            return self.live_trade_engine.get_live_trading_status()
        except Exception as e:
            logger.error(f"Error getting live trading status: {e}")
            return {}

    def set_live_trading_params(self, balance: float = None, risk_per_trade: float = None):
        """Set live trading parameters"""
        try:
            self.live_trade_engine.set_live_trading_params(balance, risk_per_trade)
        except Exception as e:
            logger.error(f"Error setting live trading params: {e}")

    def close_all_live_trades(self):
        """Close all open live trades"""
        try:
            # Get current price from latest candle
            latest_candle = self.data_manager.get_latest_candle("BTCUSD", 1)
            if latest_candle is not None:
                current_price = latest_candle['Close']
                self.live_trade_engine.close_all_live_trades(current_price)
            else:
                logger.warning("Cannot close trades: no current price available")
        except Exception as e:
            logger.error(f"Error closing all live trades: {e}")

    def _print_backtest_results(self, result):
        """Print backtest results to console"""
        try:
            print("\n" + "="*50)
            print("BACKTEST RESULTS")
            print("="*50)
            print(f"Total Trades: {result.total_trades}")
            print(f"Winning Trades: {result.winning_trades}")
            print(f"Losing Trades: {result.losing_trades}")
            print(f"Win Rate: {result.win_rate:.2f}%")
            print(f"Total Profit: ${result.total_profit:.2f}")
            print(f"Average Win: ${result.avg_win:.2f}")
            print(f"Average Loss: ${result.avg_loss:.2f}")
            print(f"Profit Factor: {result.profit_factor:.2f}")
            print(f"Max Drawdown: ${result.max_drawdown:.2f}")
            print(f"Sharpe Ratio: {result.sharpe_ratio:.2f}")
            print(f"Duration: {result.duration_days} days")
            print("="*50)

        except Exception as e:
            logger.error(f"Error printing backtest results: {e}")

    def stop(self):
        """Stop the trading bot"""
        try:
            logger.info("Stopping trading bot...")

            self.is_running = False

            if self.ws_client:
                self.ws_client.disconnect()

            logger.info("Trading bot stopped")

        except Exception as e:
            logger.error(f"Error stopping trading bot: {e}")

    def _signal_handler(self, signum, frame):
        """Handle system signals for graceful shutdown"""
        logger.info(f"Received signal {signum}, shutting down gracefully...")
        self.stop()
        sys.exit(0)

def main():
    """Main entry point"""
    try:
        import argparse

        parser = argparse.ArgumentParser(description='Trading Bot')
        parser.add_argument('--mode', choices=['live', 'backtest'], default='live',
                          help='Run mode: live trading or backtest')
        parser.add_argument('--symbol', default='BTCUSD',
                          help='Trading symbol (default: BTCUSD)')
        parser.add_argument('--rows', type=int, default=44000,
                          help='Number of 1-minute rows for backtest (default: 1000)')
        parser.add_argument('--balance', type=float, default=10000.0,
                          help='Initial balance for backtest (default: 10000)')
        parser.add_argument('--risk', type=float, default=0.02,
                          help='Risk per trade (default: 0.02)')
        parser.add_argument('--strategy', type=str, required=False,
                          help='Strategy to test (required for backtest mode)')
        parser.add_argument('--list-strategies', action='store_true',
                          help='List all available strategies and exit')

        args = parser.parse_args()

        # Handle list strategies command
        if args.list_strategies:
            from .strategy_engine import StrategyEngine
            from .data_manager import DataManager

            data_manager = DataManager()
            strategy_engine = StrategyEngine(data_manager)
            strategies = strategy_engine.get_available_strategies()

            print("Available strategies:")
            for i, strategy in enumerate(strategies, 1):
                print(f"  {i}. {strategy}")
            print(f"\nTotal: {len(strategies)} strategies")
            print("\nUsage examples:")
            print(f"  python -m trading_bot.main --mode backtest --strategy {strategies[0]}")
            print("  python -m trading_bot.main --mode live")
            return

        # Create bot instance
        bot = TradingBot()

        if args.mode == 'live':
            logger.info("Starting in live trading mode")
            bot.start_live_trading(args.symbol)
        elif args.mode == 'backtest':
            logger.info("Starting in backtest mode")

            # Validate strategy is provided for backtest mode
            if not args.strategy:
                logger.error("Strategy is required for backtest mode. Use --strategy to specify one.")
                logger.info("Use --list-strategies to see all available strategies")
                return

            # Validate strategy exists
            available_strategies = bot.strategy_engine.get_available_strategies()
            if args.strategy not in available_strategies:
                logger.error(f"Strategy '{args.strategy}' not found.")
                logger.info(f"Available strategies: {', '.join(available_strategies)}")
                logger.info("Use --list-strategies to see all available strategies")
                return

            logger.info(f"Testing strategy: {args.strategy}")

            bot.run_backtest(
                strategy_filter=args.strategy,
                symbol=args.symbol,
                rows_back=args.rows,
                initial_balance=args.balance,
                risk_per_trade=args.risk
            )

    except Exception as e:
        logger.error(f"Error in main: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
