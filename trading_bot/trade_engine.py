"""
Trade engine for both backtesting and live trading
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import logging
import pickle
import os


from .config import *
from .data_manager import DataManager
from .strategy_engine import StrategyEngine, StrategyResult
from .chart_generator import ChartGenerator
from .wave_analysis import update_waves_with_new_row_df
from .level_analysis import level_from_waves

logger = logging.getLogger(__name__)

class BacktestResult:
    """Container for backtest results"""

    def __init__(self):
        self.trades = []
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        self.total_profit = 0.0
        self.max_drawdown = 0.0
        self.win_rate = 0.0
        self.avg_win = 0.0
        self.avg_loss = 0.0
        self.profit_factor = 0.0
        self.sharpe_ratio = 0.0
        self.start_date = None
        self.end_date = None
        self.duration_days = 0

    def calculate_metrics(self):
        """Calculate performance metrics"""
        try:
            if not self.trades:
                return

            profits = [trade['profit'] for trade in self.trades]

            self.total_trades = len(self.trades)
            self.winning_trades = len([p for p in profits if p > 0])
            self.losing_trades = len([p for p in profits if p < 0])
            self.total_profit = sum(profits)

            # Win rate
            self.win_rate = (self.winning_trades / self.total_trades * 100) if self.total_trades > 0 else 0

            # Average win/loss
            wins = [p for p in profits if p > 0]
            losses = [p for p in profits if p < 0]

            self.avg_win = np.mean(wins) if wins else 0
            self.avg_loss = abs(np.mean(losses)) if losses else 0

            # Profit factor
            total_wins = sum(wins) if wins else 0
            total_losses = abs(sum(losses)) if losses else 0
            self.profit_factor = (total_wins / total_losses) if total_losses > 0 else float('inf')

            # Max drawdown
            cumulative = np.cumsum(profits)
            running_max = np.maximum.accumulate(cumulative)
            drawdown = running_max - cumulative
            self.max_drawdown = np.max(drawdown) if len(drawdown) > 0 else 0

            # Sharpe ratio (simplified)
            if len(profits) > 1:
                self.sharpe_ratio = np.mean(profits) / np.std(profits) if np.std(profits) > 0 else 0

            # Duration
            if self.trades:
                dates = [trade['entry_time'] for trade in self.trades]
                self.start_date = min(dates)
                self.end_date = max(dates)
                self.duration_days = (self.end_date - self.start_date).days

        except Exception as e:
            logger.error(f"Error calculating backtest metrics: {e}")

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'losing_trades': self.losing_trades,
            'total_profit': self.total_profit,
            'max_drawdown': self.max_drawdown,
            'win_rate': self.win_rate,
            'avg_win': self.avg_win,
            'avg_loss': self.avg_loss,
            'profit_factor': self.profit_factor,
            'sharpe_ratio': self.sharpe_ratio,
            'start_date': self.start_date,
            'end_date': self.end_date,
            'duration_days': self.duration_days,
            'trades': self.trades
        }

class TradeEngine:
    """Trade engine for both backtesting and live trading"""

    def __init__(self, data_manager: DataManager, strategy_engine: StrategyEngine, trade_mode: str = "test"):
        """
        Initialize TradeEngine

        Args:
            data_manager: DataManager instance
            strategy_engine: StrategyEngine instance
            trade_mode: Trading mode - 'test' for backtesting, 'live' for live trading
        """
        if trade_mode not in ["test", "live"]:
            raise ValueError("trade_mode must be either 'test' or 'live'")

        self.data_manager = data_manager
        self.strategy_engine = strategy_engine
        self.trade_mode = trade_mode
        self.results_cache = {}

        # Live trading attributes
        self.open_trades = []
        self.live_balance = 10000.0  # Default balance for live trading
        self.risk_per_trade = 0.02  # Default risk per trade

    def run_backtest(self, symbol: str, start_date: datetime, end_date: datetime,
                    strategy_filter: str, main_tf: int = 15, high_tf: int = 60, very_high_tf: int = 1440,
                    initial_balance: float = 10000.0, risk_per_trade: float = 0.02) -> BacktestResult:
        """
        Run sequential backtest for a specific strategy

        Args:
            symbol: Trading symbol
            start_date: Start date for backtest
            end_date: End date for backtest
            main_tf: Main timeframe
            high_tf: Higher timeframe
            very_high_tf: Very high timeframe
            initial_balance: Starting balance
            risk_per_trade: Risk percentage per trade
            strategy_filter: Strategy name to test (required)

        Returns:
            BacktestResult: Backtest results
        """
        try:
            logger.info(f"Starting backtest for {symbol} from {start_date} to {end_date}")

            # Get data
            df = self.data_manager.get_dataframe(symbol, main_tf)
            if df is None or len(df) == 0:
                logger.error(f"No data available for {symbol}")
                raise ValueError(f"No data available for {symbol}")

            # Filter data by date range
            mask = (df.index >= start_date) & (df.index <= end_date)
            df_filtered = df.loc[mask]

            if len(df_filtered) == 0:
                logger.error(f"No data in date range for {symbol}")
                raise ValueError(f"No data in date range for {symbol}")

            logger.info(f"Backtesting {len(df_filtered)} candles")

            # Run sequential backtest for the specified strategy
            logger.info(f"Running sequential backtest for strategy: {strategy_filter}")
            return self._run_sequential_backtest(
                symbol, df_filtered, main_tf, high_tf, very_high_tf,
                initial_balance, risk_per_trade, strategy_filter
            )

        except Exception as e:
            logger.error(f"Error running backtest: {e}")
            return BacktestResult()

    def process_candle(self, symbol: str, timestamp: datetime = None, candle_data: Dict = None,
                      main_tf: int = 15, high_tf: int = 60, very_high_tf: int = 1440,
                      strategy_filter: Optional[str] = None) -> Dict:
        """
        Process a new candle for both backtesting and live trading

        Args:
            symbol: Trading symbol
            timestamp: Candle timestamp (for backtest mode)
            candle_data: New candle data (for live mode)
            main_tf: Main timeframe
            high_tf: Higher timeframe
            very_high_tf: Very high timeframe
            strategy_filter: Optional strategy name to test (for backtest mode)

        Returns:
            Dict: Processing result with trade information
        """
        try:
            result = {
                'trades_closed': [],
                'trades_opened': [],
                'current_balance': self.live_balance if self.trade_mode == "live" else None
            }

            # Get current price based on mode
            if self.trade_mode == "live":
                if candle_data is None:
                    logger.error("candle_data required for live trading mode")
                    return result
                current_price = candle_data.get('Close', 0)
                current_time = datetime.now()
            else:  # backtest mode
                if timestamp is None:
                    logger.error("timestamp required for backtest mode")
                    return result
                # Get current candle data from data manager
                df = self.data_manager.get_dataframe(symbol, main_tf)
                if df is None or timestamp not in df.index:
                    return result
                current_candle = df.loc[timestamp]
                current_price = current_candle['Close']
                current_time = timestamp

            # Check for trade exits first
            if self.trade_mode == "live":
                self.open_trades, closed_trades = self._check_live_trade_exits(self.open_trades, current_price)
            else:
                # For backtest, we need the full candle data for high/low checks
                df = self.data_manager.get_dataframe(symbol, main_tf)
                current_candle = df.loc[timestamp]
                self.open_trades, closed_trades = self._check_trade_exits(self.open_trades, current_candle)

            # Process closed trades
            for trade in closed_trades:
                profit = self._calculate_trade_profit(trade, self.live_balance, self.risk_per_trade)

                if self.trade_mode == "live":
                    self.live_balance += profit

                trade_record = {
                    'entry_time': trade['entry_time'],
                    'exit_time': current_time,
                    'entry_price': trade['entry_price'],
                    'exit_price': trade.get('exit_price', current_price),
                    'profit': profit,
                    'pattern': trade['pattern'],
                    'side': trade['side']
                }
                result['trades_closed'].append(trade_record)

                if self.trade_mode == "live":
                    logger.info(f"Trade closed: {trade['pattern']} - Profit: {profit:.2f}, New Balance: {self.live_balance:.2f}")

            # Check for new trade entries
            if len(self.open_trades) < MAX_CONCURRENT_TRADES_PER_STRATEGY:
                # Analyze for trading signals
                if strategy_filter:
                    # Use specific strategy for backtest
                    strategy_result = self.strategy_engine.analyze_specific_strategy(
                        symbol, main_tf, high_tf, very_high_tf, strategy_filter
                    )
                else:
                    # Use all strategies analysis for live trading
                    strategy_result = self.strategy_engine.analyze_coc_patterns(symbol, main_tf, high_tf, very_high_tf)

                if strategy_result and strategy_result.is_valid():
                    new_trade = {
                        'entry_time': current_time,
                        'entry_price': strategy_result.entry_price,
                        'stop_loss': strategy_result.stop_loss,
                        'take_profit': strategy_result.take_profit,
                        'pattern': strategy_result.pattern_name,
                        'side': 'long' if strategy_result.entry_price < strategy_result.take_profit else 'short'
                    }
                    self.open_trades.append(new_trade)
                    result['trades_opened'].append(new_trade.copy())

                    if self.trade_mode == "live":
                        logger.info(f"New trade opened: {strategy_result.pattern_name}")
                        logger.info(f"Entry: {strategy_result.entry_price}, SL: {strategy_result.stop_loss}, TP: {strategy_result.take_profit}")

            result['current_balance'] = self.live_balance if self.trade_mode == "live" else None
            return result

        except Exception as e:
            logger.error(f"Error processing candle: {e}")
            return {'trades_closed': [], 'trades_opened': [], 'current_balance': None}

    def _check_live_trade_exits(self, open_trades: List[Dict], current_price: float) -> Tuple[List[Dict], List[Dict]]:
        """Check if any live trades should be closed based on current price"""
        try:
            remaining_trades = []
            closed_trades = []

            for trade in open_trades:
                should_close = False
                exit_price = current_price

                if trade['side'] == 'long':
                    # Check TP and SL for long trades
                    if current_price >= trade['take_profit']:
                        exit_price = trade['take_profit']
                        should_close = True
                    elif current_price <= trade['stop_loss']:
                        exit_price = trade['stop_loss']
                        should_close = True
                else:
                    # Check TP and SL for short trades
                    if current_price <= trade['take_profit']:
                        exit_price = trade['take_profit']
                        should_close = True
                    elif current_price >= trade['stop_loss']:
                        exit_price = trade['stop_loss']
                        should_close = True

                if should_close:
                    trade['exit_price'] = exit_price
                    trade['exit_time'] = datetime.now()
                    closed_trades.append(trade)
                else:
                    remaining_trades.append(trade)

            return remaining_trades, closed_trades

        except Exception as e:
            logger.error(f"Error checking live trade exits: {e}")
            return open_trades, []

    def get_live_trading_status(self) -> Dict:
        """Get current live trading status"""
        return {
            'trade_mode': self.trade_mode,
            'balance': self.live_balance,
            'open_trades': len(self.open_trades),
            'open_trade_details': self.open_trades.copy(),
            'risk_per_trade': self.risk_per_trade
        }

    def set_live_trading_params(self, balance: float = None, risk_per_trade: float = None):
        """Set live trading parameters"""
        if balance is not None:
            self.live_balance = balance
            logger.info(f"Live trading balance set to: {balance}")

        if risk_per_trade is not None:
            self.risk_per_trade = risk_per_trade
            logger.info(f"Risk per trade set to: {risk_per_trade}")

    def close_all_live_trades(self, current_price: float):
        """Close all open live trades at current market price"""
        if self.trade_mode != "live":
            logger.warning("close_all_live_trades called but trade_mode is not 'live'")
            return

        try:
            for trade in self.open_trades:
                profit = self._calculate_trade_profit(trade, self.live_balance, self.risk_per_trade, current_price)
                self.live_balance += profit

                logger.info(f"Force closed trade: {trade['pattern']} - Profit: {profit:.2f}")

            self.open_trades.clear()
            logger.info(f"All trades closed. Final balance: {self.live_balance:.2f}")

        except Exception as e:
            logger.error(f"Error closing all live trades: {e}")

    def _run_sequential_backtest(self, symbol: str, df: pd.DataFrame, main_tf: int,
                                high_tf: int, very_high_tf: int, initial_balance: float,
                                risk_per_trade: float, strategy_filter: str) -> BacktestResult:
        """Run sequential backtest using _process_chunk_with_strategy for consistency"""
        try:
            logger.info(f"Running sequential backtest for strategy: {strategy_filter}")

            # Use _process_chunk_with_strategy with the entire dataframe
            result = self._process_chunk_with_strategy(
                symbol=symbol,
                chunk=df,
                main_tf=main_tf,
                high_tf=high_tf,
                very_high_tf=very_high_tf,
                initial_balance=initial_balance,
                risk_per_trade=risk_per_trade,
                strategy_name=strategy_filter
            )

            result.calculate_metrics()
            logger.info(f"Sequential backtest completed. Total trades: {result.total_trades}")

            return result

        except Exception as e:
            logger.error(f"Error in sequential backtest: {e}")
            return BacktestResult()


    def _process_chunk_with_strategy(self, symbol: str, chunk: pd.DataFrame, main_tf: int,
                                   high_tf: int, very_high_tf: int, initial_balance: float,
                                   risk_per_trade: float, strategy_name: str) -> BacktestResult:
        """Process data with a specific strategy (sequential processing only)"""
        try:
            # Use existing data manager and strategy engine for sequential processing
            chunk_data_manager = self.data_manager
            chunk_strategy_engine = self.strategy_engine

            result = BacktestResult()
            balance = initial_balance
            open_trades = []

            # Process each candle in the chunk
            for i, (timestamp, row) in enumerate(chunk.iterrows()):
                try:
                    # Update data manager to current timestamp
                    chunk_data_manager.update_data_to_new_timestamp(symbol, timestamp, main_tf)

                    # Check for trade exits first
                    open_trades, closed_trades = self._check_trade_exits(open_trades, row)

                    # Process closed trades
                    for trade in closed_trades:
                        profit = self._calculate_trade_profit(trade, balance, risk_per_trade)
                        balance += profit

                        trade_record = {
                            'entry_time': trade['entry_time'],
                            'exit_time': timestamp,
                            'entry_price': trade['entry_price'],
                            'exit_price': trade['exit_price'],
                            'profit': profit,
                            'pattern': trade['pattern'],
                            'side': trade['side'],
                            'strategy': strategy_name
                        }
                        result.trades.append(trade_record)

                    # Check for new trade entries
                    if len(open_trades) < MAX_CONCURRENT_TRADES_PER_STRATEGY:
                        # Test specific strategy
                        strategy_result = chunk_strategy_engine.analyze_specific_strategy(
                            symbol, main_tf, high_tf, very_high_tf, strategy_name
                        )

                        if strategy_result and strategy_result.is_valid():
                            new_trade = {
                                'entry_time': timestamp,
                                'entry_price': strategy_result.entry_price,
                                'stop_loss': strategy_result.stop_loss,
                                'take_profit': strategy_result.take_profit,
                                'pattern': strategy_result.pattern_name,
                                'side': 'long' if strategy_result.entry_price < strategy_result.take_profit else 'short'
                            }
                            # plot
                            chart_path = self.chart_generator.plot_position_analysis(
                                symbol, main_tf, new_trade
                            )
                            if chart_path:
                                logger.info(f"Chart saved: {chart_path}")
                            open_trades.append(new_trade)

                    # Progress logging
                    if i % 10 == 0:
                        logger.info(f"Processed {i}/{len(chunk)} candles, Balance: {balance:.2f}")

                except Exception as e:
                    logger.error(f"Error processing row {i} for strategy {strategy_name}: {e}")
                    continue

            # Close any remaining open trades
            for trade in open_trades:
                profit = self._calculate_trade_profit(trade, balance, risk_per_trade, chunk.iloc[-1]['Close'])
                balance += profit

                trade_record = {
                    'entry_time': trade['entry_time'],
                    'exit_time': chunk.index[-1],
                    'entry_price': trade['entry_price'],
                    'exit_price': chunk.iloc[-1]['Close'],
                    'profit': profit,
                    'pattern': trade['pattern'],
                    'side': trade['side'],
                    'strategy': strategy_name
                }
                result.trades.append(trade_record)

            return result

        except Exception as e:
            logger.error(f"Error processing data with strategy {strategy_name}: {e}")
            return BacktestResult()

    def _check_trade_exits(self, open_trades: List[Dict], current_candle: pd.Series) -> Tuple[List[Dict], List[Dict]]:
        """Check if any open trades should be closed"""
        try:
            remaining_trades = []
            closed_trades = []

            for trade in open_trades:
                exit_price = None

                if trade['side'] == 'long':
                    # Check TP and SL for long trades
                    if current_candle['High'] >= trade['take_profit']:
                        exit_price = trade['take_profit']
                    elif current_candle['Low'] <= trade['stop_loss']:
                        exit_price = trade['stop_loss']
                else:
                    # Check TP and SL for short trades
                    if current_candle['Low'] <= trade['take_profit']:
                        exit_price = trade['take_profit']
                    elif current_candle['High'] >= trade['stop_loss']:
                        exit_price = trade['stop_loss']

                if exit_price:
                    trade['exit_price'] = exit_price
                    closed_trades.append(trade)
                else:
                    remaining_trades.append(trade)

            return remaining_trades, closed_trades

        except Exception as e:
            logger.error(f"Error checking trade exits: {e}")
            return open_trades, []

    def _calculate_trade_profit(self, trade: Dict, balance: float, risk_per_trade: float,
                               exit_price: float = None) -> float:
        """Calculate profit for a trade"""
        try:
            if exit_price is None:
                exit_price = trade.get('exit_price', trade['entry_price'])

            # Calculate position size based on risk
            risk_amount = balance * risk_per_trade
            stop_distance = abs(trade['entry_price'] - trade['stop_loss'])

            if stop_distance == 0:
                return 0

            position_size = risk_amount / stop_distance

            # Calculate profit
            if trade['side'] == 'long':
                profit = (exit_price - trade['entry_price']) * position_size
            else:
                profit = (trade['entry_price'] - exit_price) * position_size

            return profit

        except Exception as e:
            logger.error(f"Error calculating trade profit: {e}")
            return 0

    def save_results(self, result: BacktestResult, filename: str):
        """Save backtest results to file"""
        try:
            os.makedirs('backtest_results', exist_ok=True)
            filepath = os.path.join('backtest_results', filename)

            with open(filepath, 'wb') as f:
                pickle.dump(result.to_dict(), f)

            logger.info(f"Backtest results saved to {filepath}")

        except Exception as e:
            logger.error(f"Error saving backtest results: {e}")

    def load_results(self, filename: str) -> Optional[BacktestResult]:
        """Load backtest results from file"""
        try:
            filepath = os.path.join('backtest_results', filename)

            with open(filepath, 'rb') as f:
                data = pickle.load(f)

            result = BacktestResult()
            result.__dict__.update(data)

            logger.info(f"Backtest results loaded from {filepath}")
            return result

        except Exception as e:
            logger.error(f"Error loading backtest results: {e}")
            return None
